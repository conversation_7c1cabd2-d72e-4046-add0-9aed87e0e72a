"""
Patient Study Module - DICOM PS3.3 C.7.2.2

The Patient Study Module contains attributes that provide information about 
the Patient at the time the Study started.
"""
from datetime import datetime, date
from typing import Optional
from pydicom import Dataset
from .base_module import BaseModule
from ...enums.patient_study_enums import SmokingStatus, PregnancyStatus, PatientSexNeutered
from ...validators.modules.base_validator import ValidationConfig
from ...validators.modules.patient_study_validator import PatientStudyValidator
from ...validators import ValidationResult
from ...utils.dicom_formatters import format_date_value, format_enum_value


class PatientStudyModule(BaseModule):
    """Patient Study Module implementation for DICOM PS3.3 C.7.2.2.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that provide information about the Patient at the time the Study started.
    
    Usage:
        # Create with required elements (all Type 2C/3, so empty instance is valid)
        patient_study = PatientStudyModule.from_required_elements()
        
        # Add patient demographics
        patient_study.with_patient_demographics(
            patients_age="45Y",
            patients_size=1.75,
            patients_weight=70.5,
            smoking_status=SmokingStatus.NO
        )
        
        # Add medical information
        patient_study.with_medical_information(
            medical_alerts="Drug allergies: Penicillin",
            allergies="Penicillin, Shellfish"
        )
        
        # Add pregnancy information (for female patients)
        patient_study.with_pregnancy_information(
            pregnancy_status=PregnancyStatus.NOT_PREGNANT,
            last_menstrual_date="20240101"
        )
        
        # Validate
        result = patient_study.validate()
    """

    @classmethod
    def from_required_elements(cls) -> 'PatientStudyModule':
        """Create PatientStudyModule from all required (Type 1 and Type 2) data elements.
        
        Note: All elements in this module are Type 2C or Type 3, so no required elements.
        
        Returns:
            PatientStudyModule: New empty dataset instance
        """
        return cls()
    
    def with_optional_elements(self, **kwargs) -> 'PatientStudyModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Patient Study Module has Type 3 elements, but they are accessed via specialized methods.
        This method is provided for API consistency but redirects to specialized methods.
        Use with_patient_demographics(), with_medical_information(), etc. instead.
        
        Args:
            **kwargs: Optional elements (redirected to specialized methods)
            
        Returns:
            PatientStudyModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided (use specialized methods instead)
        """
        if kwargs:
            raise ValueError(f"PatientStudyModule optional elements must be added via specialized methods (with_patient_demographics, with_medical_information, etc.). Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    def with_patient_demographics(
        self,
        patients_age: str | None = None,
        patients_size: float | None = None,
        patients_weight: float | None = None,
        patients_body_mass_index: float | None = None,
        measured_ap_dimension: float | None = None,
        measured_lateral_dimension: float | None = None,
        patients_size_code_sequence: list[Dataset] | None = None,
        smoking_status: str | SmokingStatus | None = None,
        occupation: str | None = None
    ) -> 'PatientStudyModule':
        """Add patient demographic information.
        
        Args:
            patients_age (str | None): Age of the Patient (0010,1010) Type 3
            patients_size (float | None): Length or size of the Patient, in meters (0010,1020) Type 3
            patients_weight (float | None): Weight of the Patient, in kilograms (0010,1030) Type 3
            patients_body_mass_index (float | None): Body Mass Index in kg/m2 (0010,1022) Type 3
            measured_ap_dimension (float | None): Thickness in mm, antero-posterior dimension (0010,1023) Type 3
            measured_lateral_dimension (float | None): Side-to-side dimension in mm (0010,1024) Type 3
            patients_size_code_sequence (list[Dataset] | None): Patient's size category codes (0010,1021) Type 3
            smoking_status (str | SmokingStatus | None): Whether Patient smokes (0010,21A0) Type 3
            occupation (str | None): Occupation of the Patient (0010,2180) Type 3
            
        Returns:
            PatientStudyModule: Self with demographic elements added
        """
        self._set_attribute_if_not_none('PatientAge', patients_age)
        self._set_attribute_if_not_none('PatientSize', patients_size)
        self._set_attribute_if_not_none('PatientWeight', patients_weight)
        self._set_attribute_if_not_none('PatientBodyMassIndex', patients_body_mass_index)
        self._set_attribute_if_not_none('MeasuredAPDimension', measured_ap_dimension)
        self._set_attribute_if_not_none('MeasuredLateralDimension', measured_lateral_dimension)
        self._set_attribute_if_not_none('PatientSizeCodeSequence', patients_size_code_sequence)
        if smoking_status is not None:
            self.SmokingStatus = format_enum_value(smoking_status)
        self._set_attribute_if_not_none('Occupation', occupation)
        return self
    
    def with_medical_information(
        self,
        medical_alerts: str | None = None,
        allergies: str | None = None,
        additional_patient_history: str | None = None,
        patient_state: str | None = None,
        admitting_diagnoses_description: str | None = None,
        admitting_diagnoses_code_sequence: list[Dataset] | None = None,
        principal_diagnosis_code_sequence: list[Dataset] | None = None,
        primary_diagnosis_code_sequence: list[Dataset] | None = None,
        secondary_diagnoses_code_sequence: list[Dataset] | None = None,
        histological_diagnoses_code_sequence: list[Dataset] | None = None
    ) -> 'PatientStudyModule':
        """Add medical information and diagnoses.
        
        Args:
            medical_alerts (str | None): Conditions to alert medical staff (0010,2000) Type 3
            allergies (str | None): Prior reactions to contrast agents or allergies (0010,2110) Type 3
            additional_patient_history (str | None): Additional medical history (0010,21B0) Type 3
            patient_state (str | None): Description of Patient state (0038,0500) Type 3
            admitting_diagnoses_description (str | None): Reason patient sought care (0008,1080) Type 3
            admitting_diagnoses_code_sequence (list[Dataset] | None): Coded reasons for care (0008,1084) Type 3
            principal_diagnosis_code_sequence (list[Dataset] | None): Condition chiefly responsible (0008,1301) Type 3
            primary_diagnosis_code_sequence (list[Dataset] | None): Most serious condition (0008,1302) Type 3
            secondary_diagnoses_code_sequence (list[Dataset] | None): Coexisting conditions (0008,1303) Type 3
            histological_diagnoses_code_sequence (list[Dataset] | None): Histopathology confirmed (0008,1304) Type 3
            
        Returns:
            PatientStudyModule: Self with medical information elements added
        """
        self._set_attribute_if_not_none('MedicalAlerts', medical_alerts)
        self._set_attribute_if_not_none('Allergies', allergies)
        self._set_attribute_if_not_none('AdditionalPatientHistory', additional_patient_history)
        self._set_attribute_if_not_none('PatientState', patient_state)
        self._set_attribute_if_not_none('AdmittingDiagnosesDescription', admitting_diagnoses_description)
        self._set_attribute_if_not_none('AdmittingDiagnosesCodeSequence', admitting_diagnoses_code_sequence)
        self._set_attribute_if_not_none('PrincipalDiagnosisCodeSequence', principal_diagnosis_code_sequence)
        self._set_attribute_if_not_none('PrimaryDiagnosisCodeSequence', primary_diagnosis_code_sequence)
        self._set_attribute_if_not_none('SecondaryDiagnosesCodeSequence', secondary_diagnoses_code_sequence)
        self._set_attribute_if_not_none('HistologicalDiagnosesCodeSequence', histological_diagnoses_code_sequence)
        return self
    
    def with_pregnancy_information(
        self,
        pregnancy_status: int | PregnancyStatus | None = None,
        last_menstrual_date: str | datetime | date | None = None
    ) -> 'PatientStudyModule':
        """Add pregnancy-related information.
        
        Args:
            pregnancy_status (int | PregnancyStatus | None): Pregnancy state (0010,21C0) Type 3
            last_menstrual_date (str | datetime | date | None): Date of last menstrual period (0010,21D0) Type 3
            
        Returns:
            PatientStudyModule: Self with pregnancy information elements added
        """
        if pregnancy_status is not None:
            if isinstance(pregnancy_status, PregnancyStatus):
                self.PregnancyStatus = pregnancy_status.value
            else:
                self.PregnancyStatus = pregnancy_status
        if last_menstrual_date is not None:
            self.LastMenstrualDate = format_date_value(last_menstrual_date)
        return self
    
    def with_visit_information(
        self,
        admission_id: str | None = None,
        issuer_of_admission_id_sequence: list[Dataset] | None = None,
        reason_for_visit: str | None = None,
        reason_for_visit_code_sequence: list[Dataset] | None = None,
        service_episode_id: str | None = None,
        issuer_of_service_episode_id_sequence: list[Dataset] | None = None,
        service_episode_description: str | None = None
    ) -> 'PatientStudyModule':
        """Add visit and admission information.
        
        Args:
            admission_id (str | None): Identifier of the Visit (0038,0010) Type 3
            issuer_of_admission_id_sequence (list[Dataset] | None): Assigning Authority for admission ID (0038,0014) Type 3
            reason_for_visit (str | None): Reason for this visit (0032,1066) Type 3
            reason_for_visit_code_sequence (list[Dataset] | None): Coded reason for visit (0032,1067) Type 3
            service_episode_id (str | None): Identifier of the Service Episode (0038,0060) Type 3
            issuer_of_service_episode_id_sequence (list[Dataset] | None): Assigning Authority for episode ID (0038,0064) Type 3
            service_episode_description (str | None): Description of service episode type (0038,0062) Type 3
            
        Returns:
            PatientStudyModule: Self with visit information elements added
        """
        self._set_attribute_if_not_none('AdmissionID', admission_id)
        self._set_attribute_if_not_none('IssuerOfAdmissionIDSequence', issuer_of_admission_id_sequence)
        self._set_attribute_if_not_none('ReasonForVisit', reason_for_visit)
        self._set_attribute_if_not_none('ReasonForVisitCodeSequence', reason_for_visit_code_sequence)
        self._set_attribute_if_not_none('ServiceEpisodeID', service_episode_id)
        self._set_attribute_if_not_none('IssuerOfServiceEpisodeIDSequence', issuer_of_service_episode_id_sequence)
        self._set_attribute_if_not_none('ServiceEpisodeDescription', service_episode_description)
        return self

    def with_non_human_organism_info(
        self,
        patients_sex_neutered: str | PatientSexNeutered
    ) -> 'PatientStudyModule':
        """Add non-human organism information with required conditional logic.

        Note: Patient's Sex Neutered (0010,2203) is Type 2C - required if Patient is a non-human organism.

        Args:
            patients_sex_neutered (str | PatientSexNeutered): Whether procedure performed to render sterile (0010,2203) Type 2C

        Returns:
            PatientStudyModule: Self with non-human organism elements added
        """
        self.PatientSexNeutered = format_enum_value(patients_sex_neutered)
        return self

    def with_gender_identity(
        self,
        gender_identity_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add gender identity information.

        Args:
            gender_identity_sequence (list[Dataset]): Individual's personal sense of gender (0010,0041) Type 3

        Returns:
            PatientStudyModule: Self with gender identity elements added
        """
        self.GenderIdentitySequence = gender_identity_sequence
        return self

    def with_sex_parameters_for_clinical_use(
        self,
        sex_parameters_for_clinical_use_category_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add sex parameters for clinical use.

        Args:
            sex_parameters_for_clinical_use_category_sequence (list[Dataset]): Clinical use guidance (0010,0043) Type 3

        Returns:
            PatientStudyModule: Self with sex parameters elements added
        """
        self.SexParametersForClinicalUseCategorySequence = sex_parameters_for_clinical_use_category_sequence
        return self

    def with_person_names_to_use(
        self,
        person_names_to_use_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add preferred names for addressing the person.

        Args:
            person_names_to_use_sequence (list[Dataset]): Names to use when addressing person (0010,0011) Type 3

        Returns:
            PatientStudyModule: Self with person names elements added
        """
        self.PersonNamesToUseSequence = person_names_to_use_sequence
        return self

    def with_third_person_pronouns(
        self,
        third_person_pronouns_sequence: list[Dataset]
    ) -> 'PatientStudyModule':
        """Add third person pronouns information.

        Args:
            third_person_pronouns_sequence (list[Dataset]): Pronouns to use in reference (0010,0014) Type 3

        Returns:
            PatientStudyModule: Self with pronouns elements added
        """
        self.ThirdPersonPronounsSequence = third_person_pronouns_sequence
        return self

    @staticmethod
    def create_gender_identity_item(
        gender_identity_code_sequence: list[Dataset],
        effective_start_datetime: Optional[str | datetime] = None,
        effective_stop_datetime: Optional[str | datetime] = None,
        gender_identity_comment: Optional[str] = None
    ) -> Dataset:
        """Create an item for Gender Identity Sequence (0010,0041).

        Args:
            gender_identity_code_sequence (list[Dataset]): Coded gender identity (0010,0044) Type 1
            effective_start_datetime (str | datetime | None): Start date/time (0040,A034) Type 3
            effective_stop_datetime (str | datetime | None): Stop date/time (0040,A035) Type 3
            gender_identity_comment (str | None): Comments on gender identity (0010,0045) Type 3

        Returns:
            Dataset: Sequence item with gender identity information
        """
        item = Dataset()
        item.GenderIdentityCodeSequence = gender_identity_code_sequence
        if effective_start_datetime is not None:
            item.EffectiveStartDateTime = effective_start_datetime
        if effective_stop_datetime is not None:
            item.EffectiveStopDateTime = effective_stop_datetime
        if gender_identity_comment is not None:
            item.GenderIdentityComment = gender_identity_comment
        return item

    @staticmethod
    def create_person_name_to_use_item(
        name_to_use: str,
        effective_start_datetime: Optional[str | datetime] = None,
        effective_stop_datetime: Optional[str | datetime] = None,
        name_to_use_comment: Optional[str] = None
    ) -> Dataset:
        """Create an item for Person Names to Use Sequence (0010,0011).

        Args:
            name_to_use (str): Name to use when addressing person (0010,0012) Type 1
            effective_start_datetime (str | datetime | None): Start date/time (0040,A034) Type 3
            effective_stop_datetime (str | datetime | None): Stop date/time (0040,A035) Type 3
            name_to_use_comment (str | None): Explanation of name usage (0010,0013) Type 3

        Returns:
            Dataset: Sequence item with name information
        """
        item = Dataset()
        item.NameToUse = name_to_use
        if effective_start_datetime is not None:
            item.EffectiveStartDateTime = effective_start_datetime
        if effective_stop_datetime is not None:
            item.EffectiveStopDateTime = effective_stop_datetime
        if name_to_use_comment is not None:
            item.NameToUseComment = name_to_use_comment
        return item

    @property
    def has_demographic_info(self) -> bool:
        """Check if demographic information is present.

        Returns:
            bool: True if any demographic elements are present
        """
        return any(hasattr(self, attr) for attr in [
            'PatientAge', 'PatientSize', 'PatientWeight', 'PatientBodyMassIndex'
        ])

    @property
    def has_medical_info(self) -> bool:
        """Check if medical information is present.

        Returns:
            bool: True if any medical elements are present
        """
        return any(hasattr(self, attr) for attr in [
            'MedicalAlerts', 'Allergies', 'AdditionalPatientHistory'
        ])

    @property
    def has_pregnancy_info(self) -> bool:
        """Check if pregnancy information is present.

        Returns:
            bool: True if pregnancy-related elements are present
        """
        return (hasattr(self, 'PregnancyStatus') or hasattr(self, 'LastMenstrualDate'))

    @property
    def has_visit_info(self) -> bool:
        """Check if visit information is present.

        Returns:
            bool: True if visit-related elements are present
        """
        return any(hasattr(self, attr) for attr in [
            'AdmissionID', 'ReasonForVisit', 'ServiceEpisodeID'
        ])

    @property
    def is_non_human_organism(self) -> bool:
        """Check if this represents a non-human organism.

        Returns:
            bool: True if sex neutered information indicates non-human organism
        """
        return hasattr(self, 'PatientSexNeutered')

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Patient Study Module instance.

        Args:
            config (ValidationConfig | None): Optional validation configuration

        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return PatientStudyValidator.validate(self, config)
